<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753774208074_hudbth0bc" time="2025/07/29 15:30">
    <content>
      项目初始化状态：
      - 项目路径：c:\Users\<USER>\Desktop\测试
      - 已有.augment目录，包含rules/quanju.md规则文件
      - 已有.promptx目录，PromptX系统已初始化
      - MCP实例：mcp-17948 (cursor)
      - 当前激活角色：nuwa (AI角色创造专家)
      - 规则要求：1.优先使用命令工具 2.使用Context7工具查看API文档 3.记录开发细节到promptx记忆 4.修改前先回忆记忆和查看源代码
    </content>
    <tags>#最佳实践 #工具使用</tags>
  </item>
  <item id="mem_1753774229300_nmceyuy2f" time="2025/07/29 15:30">
    <content>
      MCP工具系统分析：
      - PromptX系统已完全初始化并运行正常
      - 项目注册表显示当前无项目级工具资源
      - 未发现Context7工具配置
      - 需要按照规则要求初始化Context7工具或其他API文档查看工具
      - 当前可用系统角色：assistant, luban, noface, nuwa, sean
      - 建议：可能需要创建或配置Context7工具来满足规则要求
    </content>
    <tags>#最佳实践 #工具使用</tags>
  </item>
  <item id="mem_1753781206157_qcjq5pjba" time="2025/07/29 17:26">
    <content>
      产品经理角色提示词更新完成：
      1. 添加&quot;禁止写代码&quot;规则 - 产品经理专注规划设计，绝不编写任何代码实现
      2. 添加&quot;优先使用命令工具&quot;规范 - 不生成脚本，优先使用现有命令工具
      3. 添加&quot;Context7工具使用规范&quot; - 需要API文档时使用Context7，不需要时跳过
      4. 添加&quot;promptx记忆管理&quot;要求 - 详细记录开发细节到promptx记忆功能
      5. 添加&quot;修改前回忆检查&quot;流程 - 项目修改前必须先回忆相关信息
      6. 强化&quot;角色边界清晰&quot;原则 - 专注产品规划，技术实现交给开发角色
    
      更新文件：
      - product-manager.role.md：knowledge和principle部分
      - project-planning.execution.md：rule部分
      项目注册表已刷新，更新生效
    </content>
    <tags>#最佳实践 #流程管理 #工具使用</tags>
  </item>
</memory>