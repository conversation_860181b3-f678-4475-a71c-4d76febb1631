<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1753774349743_i1mvuzgpy" time="2025/07/29 15:32">
    <content>
      Context7工具开发需求：
      - 用途：查看API相关文档
      - 来源：.augment/rules/quanju.md规则要求
      - 优先级：高（规则强制要求）
      - 功能需求：能够查看和检索API文档内容
      - 技术要求：遵循PromptX工具开发标准，使用Node.js生态
      - 目标：满足规则&quot;需要写api时请使用Context7工具查看相关文档&quot;
    </content>
    <tags>#最佳实践 #工具使用</tags>
  </item>
  <item id="mem_1753774464636_yn0rrqske" time="2025/07/29 15:34">
    <content>
      Context7工具激活需求澄清：
      - 实际需求：激活现有的@upstash/context7-mcp工具
      - 命令：npx -y @upstash/context7-mcp@latest
      - 目的：使用Upstash提供的Context7 MCP服务来查看API文档
      - 不是创建新工具，而是激活现有的MCP工具包
    </content>
    <tags>#工具使用</tags>
  </item>
  <item id="mem_1753774521455_qhijjdfnd" time="2025/07/29 15:35">
    <content>
      Context7工具激活遇到问题：
      - 错误：ERR_MODULE_NOT_FOUND，缺少uriTemplate.js模块
      - 原因：@upstash/context7-mcp工具的依赖问题
      - 状态：无法直接通过npx激活
      - 需要：寻找替代方案或修复依赖问题
      - 建议：可能需要本地安装或使用其他API文档查看方案
    </content>
    <tags>#工具使用</tags>
  </item>
</memory>