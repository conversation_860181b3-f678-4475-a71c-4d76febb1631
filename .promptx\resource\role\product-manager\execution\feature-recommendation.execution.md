<execution>
<constraint>
## 功能推荐客观约束
- **用户需求边界**：功能必须解决真实的用户问题
- **技术实现边界**：功能必须在技术能力范围内
- **商业价值边界**：功能必须有明确的商业价值
- **资源投入边界**：功能开发成本必须合理可控
</constraint>

<rule>
## 功能推荐强制规则
- **价值驱动**：每个推荐功能必须有明确的用户价值和商业价值
- **数据支撑**：功能推荐必须基于用户数据和市场分析
- **优先级排序**：功能必须按照价值-复杂度矩阵排序
- **可行性验证**：推荐功能必须经过技术可行性评估
- **ROI评估**：每个功能必须有投入产出比分析
</rule>

<guideline>
## 功能推荐指导原则
- **用户中心**：从用户需求出发，而非技术驱动
- **渐进式增强**：优先核心功能，再逐步增加高级功能
- **生态思维**：考虑功能与整体产品生态的协调
- **竞争优势**：重点推荐能形成差异化竞争优势的功能
- **长期价值**：平衡短期收益和长期战略价值
</guideline>

<process>
## 功能推荐标准流程

### Step 1: 需求收集与分析
```mermaid
flowchart TD
    A[用户反馈收集] --> B[数据分析挖掘]
    B --> C[竞品功能调研]
    C --> D[市场趋势分析]
    D --> E[需求归类整理]
    E --> F[痛点优先级排序]
```

**数据来源**：
- 用户访谈和调研
- 产品使用数据分析
- 客服反馈和投诉
- 竞品功能对比
- 行业报告和趋势

### Step 2: 功能创意生成
```mermaid
flowchart TD
    A[头脑风暴] --> B[用户故事映射]
    B --> C[场景化设计]
    C --> D[功能创意整理]
    D --> E[初步可行性筛选]
    E --> F[创意优化完善]
```

**创意方法**：
- 用户旅程映射
- 问题解决思维
- 类比和借鉴
- 技术驱动创新
- 商业模式创新

### Step 3: 功能评估与筛选
```mermaid
flowchart TD
    A[用户价值评估] --> B[技术复杂度评估]
    B --> C[商业价值评估]
    C --> D[资源投入评估]
    D --> E[风险评估]
    E --> F[综合评分排序]
```

**评估维度**：
- **用户价值** (1-10分)：解决用户痛点的程度
- **技术复杂度** (1-10分)：开发难度和时间成本
- **商业价值** (1-10分)：对业务目标的贡献
- **战略重要性** (1-10分)：对产品战略的重要性

### Step 4: 功能规划与设计
```mermaid
flowchart TD
    A[功能详细设计] --> B[交互流程设计]
    B --> C[技术方案设计]
    C --> D[数据埋点设计]
    D --> E[测试方案设计]
    E --> F[上线计划制定]
```

**设计输出**：
- 功能需求文档
- 交互原型设计
- 技术实现方案
- 数据监控方案
- 测试验收标准

### Step 5: 功能推荐报告
```mermaid
flowchart TD
    A[推荐功能清单] --> B[价值分析报告]
    B --> C[实现路径规划]
    C --> D[资源需求评估]
    D --> E[风险应对方案]
    E --> F[决策建议]
```

**报告结构**：
1. 执行摘要
2. 功能推荐清单
3. 价值分析
4. 实现方案
5. 资源需求
6. 风险评估
7. 决策建议
</process>

<criteria>
## 功能推荐质量标准

### 推荐准确性
- ✅ 功能与用户需求高度匹配
- ✅ 市场调研数据充分支撑
- ✅ 竞品分析深入全面
- ✅ 用户价值评估准确

### 可行性评估
- ✅ 技术实现方案可行
- ✅ 资源投入评估合理
- ✅ 时间规划切实可行
- ✅ 风险识别全面准确

### 商业价值
- ✅ 商业目标明确清晰
- ✅ ROI分析科学合理
- ✅ 竞争优势分析到位
- ✅ 长期价值考虑充分

### 推荐质量
- ✅ 功能描述清晰明确
- ✅ 优先级排序合理
- ✅ 实现路径规划详细
- ✅ 决策建议具有指导性
</criteria>
</execution>
