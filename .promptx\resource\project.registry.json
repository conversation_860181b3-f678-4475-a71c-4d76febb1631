{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-29T09:26:33.164Z", "updatedAt": "2025-07-29T09:26:33.166Z", "resourceCount": 5}, "resources": [{"id": "feature-recommendation", "source": "project", "protocol": "execution", "name": "Feature Recommendation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/feature-recommendation.execution.md", "metadata": {"createdAt": "2025-07-29T09:26:33.165Z", "updatedAt": "2025-07-29T09:26:33.165Z", "scannedAt": "2025-07-29T09:26:33.165Z", "path": "role/product-manager/execution/feature-recommendation.execution.md"}}, {"id": "project-planning", "source": "project", "protocol": "execution", "name": "Project Planning 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/product-manager/execution/project-planning.execution.md", "metadata": {"createdAt": "2025-07-29T09:26:33.165Z", "updatedAt": "2025-07-29T09:26:33.165Z", "scannedAt": "2025-07-29T09:26:33.165Z", "path": "role/product-manager/execution/project-planning.execution.md"}}, {"id": "product-manager", "source": "project", "protocol": "role", "name": "Product Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/product-manager/product-manager.role.md", "metadata": {"createdAt": "2025-07-29T09:26:33.165Z", "updatedAt": "2025-07-29T09:26:33.165Z", "scannedAt": "2025-07-29T09:26:33.165Z", "path": "role/product-manager/product-manager.role.md"}}, {"id": "product-planning", "source": "project", "protocol": "thought", "name": "Product Planning 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/product-manager/thought/product-planning.thought.md", "metadata": {"createdAt": "2025-07-29T09:26:33.166Z", "updatedAt": "2025-07-29T09:26:33.166Z", "scannedAt": "2025-07-29T09:26:33.166Z", "path": "role/product-manager/thought/product-planning.thought.md"}}, {"id": "user-analysis", "source": "project", "protocol": "thought", "name": "User Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/product-manager/thought/user-analysis.thought.md", "metadata": {"createdAt": "2025-07-29T09:26:33.166Z", "updatedAt": "2025-07-29T09:26:33.166Z", "scannedAt": "2025-07-29T09:26:33.166Z", "path": "role/product-manager/thought/user-analysis.thought.md"}}], "stats": {"totalResources": 5, "byProtocol": {"execution": 2, "role": 1, "thought": 2}, "bySource": {"project": 5}}}